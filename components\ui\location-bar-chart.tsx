import React from 'react';

export interface LocationData {
  location: string;
  percentage: number;
}

interface LocationBarChartProps {
  data: LocationData[];
  title: string;
  className?: string;
  maxItems?: number;
}

export function LocationBarChart({ 
  data, 
  title, 
  className = "",
  maxItems = 6 
}: LocationBarChartProps) {
  // Ordenar por percentual (maior para menor) e limitar itens
  const sortedData = data
    .filter(item => item.percentage > 0)
    .sort((a, b) => b.percentage - a.percentage)
    .slice(0, maxItems);

  if (sortedData.length === 0) {
    return (
      <div className={`p-4 ${className}`}>
        <h4 className="font-semibold text-sm text-gray-900 dark:text-white mb-3">
          {title}
        </h4>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Nenhum dado disponível
        </p>
      </div>
    );
  }

  // Calcular total dos percentuais e usar 100% como base para normalização
  const totalPercentage = sortedData.reduce((sum, item) => sum + item.percentage, 0);
  const maxPercentage = Math.max(100, totalPercentage); // Usar 100% como mínimo base

  return (
    <div className={`p-4 ${className}`}>
      <h4 className="font-semibold text-sm text-gray-900 dark:text-white mb-3">
        {title}
      </h4>
      
      <div className="space-y-3">
        {sortedData.map((item, index) => {
          // Calcular largura da barra (percentual relativo ao máximo)
          const barWidth = (item.percentage / maxPercentage) * 100;
          
          return (
            <div key={`${item.location}-${index}`} className="space-y-1">
              {/* Nome e percentual acima da barra */}
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-gray-700 dark:text-gray-300 truncate">
                  {item.location}
                </span>
                <span className="text-xs font-semibold text-gray-900 dark:text-white">
                  {item.percentage.toFixed(2)}%
                </span>
              </div>
              
              {/* Barra de progresso */}
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                                        className="bg-[#080210] dark:bg-white h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${barWidth}%` }}
                />
              </div>
            </div>
          );
        })}
      </div>
      
      {data.length > maxItems && (
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
          Mostrando {maxItems} de {data.length} locais
        </p>
      )}
    </div>
  );
} 


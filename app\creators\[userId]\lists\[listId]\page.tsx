'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/use-auth-v2';
import { useFirebaseAuth } from '@/hooks/use-clerk-auth';
import { ColumnDef } from "@tanstack/react-table";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { DataTable } from '@/components/ui/data-table';
import { useDataTable } from '@/hooks/use-data-table';
import { createInfluencerColumns, Influenciador as InfluencerType } from '@/lib/table-columns/influencers';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  ArrowLeft,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Copy,
  Shield,
  List,
  Users,
  Tag,
  Calendar,
  ImageIcon,
  Settings,
  Download,
  Upload,
  ArrowRight,
  Share2
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { Loader } from '@/components/ui/loader';
import { AddProfileToListModal } from '@/components/ui/add-profile-to-list-modal';
import { ShareListDialog } from '@/components/ui/share-list-dialog';
import { SendToProposalModal } from '@/components/ui/send-to-proposal-modal';

// Tipos da lista
interface Lista {
  id: string;
  nome: string;
  tipoLista: 'estática' | 'dinâmica';
  tipoObjeto: 'influenciadores' | 'marcas' | 'campanhas' | 'conteúdo';
  tamanho: number;
  criadoPor: string;
  criadoPorNome: string;
  dataCriacao: Date;
  ultimaAtualizacao: Date;
  descricao?: string;
  tags?: string[];
  criterios?: {
    campo: string;
    operador: string;
    valor: string;
  }[];
}

// Tipos específicos para cada objeto
interface Influenciador {
  id: string;
  nome: string;
  usuario: string;
  seguidores: number;
  engajamento: number;
  nicho: string;
  localizacao: string;
  foto?: string;
  status: 'ativo' | 'inativo' | 'pendente';
}

interface Marca {
  id: string;
  nome: string;
  setor: string;
  orcamento: number;
  status: 'ativo' | 'inativo' | 'negociando';
  contato: string;
  logo?: string;
  funcionarios: number;
}

interface Campanha {
  id: string;
  nome: string;
  brand: string;
  budget: number;
  status: 'planejamento' | 'ativo' | 'finalizado' | 'cancelado';
  dataInicio: Date;
  dataFim: Date;
  alcance: number;
}

interface Conteudo {
  id: string;
  nome: string;
  formato: 'video' | 'imagem' | 'texto' | 'audio';
  tamanho: number;
  dataUpload: Date;
  engajamento?: number;
  autor: string;
  url?: string;
}

interface PageProps {
  params: Promise<{
    userId: string;
    listId: string;
  }>;
}

export default function ListaDetailPage({ params }: PageProps) {
  const { currentUser, isLoading, getToken } = useAuth();
  const { firebaseUser } = useFirebaseAuth();
  const router = useRouter();
  const [userId, setUserId] = useState<string | null>(null);
  const [listId, setListId] = useState<string | null>(null);

  // Resolver parâmetros
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params;
      setUserId(resolvedParams.userId);
      setListId(resolvedParams.listId);
      setDataLoaded(false); // Resetar flag quando parâmetros mudarem
    };
    resolveParams();
  }, [params]);

  // Verificação de acesso - memoizada para evitar loops infinitos
  const isOwnProfile = useMemo(() => {
    return currentUser?.id === userId;
  }, [currentUser?.id, userId]);

  const canAccess = isOwnProfile;

  // Estados da lista
  const [lista, setLista] = useState<Lista | null>(null);
  const [items, setItems] = useState<any[]>([]);
  const [loadingItems, setLoadingItems] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false); // Flag para controlar se os dados já foram carregados

  // Estados de filtros (removidos pois DataTable gerencia internamente)
  
  // Estado da aba ativa
  const [activeTab, setActiveTab] = useState<'perfis' | 'agregados'>('perfis');
  
  // Estado do modal de adicionar perfis
  const [showAddProfileModal, setShowAddProfileModal] = useState(false);

  // Estado do modal de compartilhamento
  const [showShareDialog, setShowShareDialog] = useState(false);

  // Estado para envio para propostas
  const [showSendToProposalModal, setShowSendToProposalModal] = useState(false);

  // Função para carregar influenciadores da lista - memoizada para evitar loops
  const loadInfluencersFromList = useCallback(async () => {
    if (!firebaseUser || !listId) return;

    try {
      setLoadingItems(true);
      console.log('🔄 Carregando itens da lista:', listId);

      const token = await getToken();

      const response = await fetch(`/api/lists/${listId}/items`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Itens da lista carregados:', {
        data,
        itensCount: data.itens?.length || 0,
        total: data.total,
        itens: data.itens
      });

      // Buscar dados completos dos influenciadores
      const influencerIds = data.itens?.map((item: any) => item.itemId) || [];
      let influencersData: any[] = [];

      console.log('🔍 IDs dos influenciadores encontrados:', influencerIds);

      if (influencerIds.length > 0) {
        try {
          // Buscar dados dos influenciadores
          const influencersResponse = await fetch('/api/influencers', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
          });

          if (influencersResponse.ok) {
            const allInfluencers = await influencersResponse.json();
            // Filtrar apenas os influenciadores que estão na lista
            influencersData = allInfluencers.filter((inf: any) => influencerIds.includes(inf.id));
          }
        } catch (error) {
          console.error('❌ Erro ao buscar dados dos influenciadores:', error);
        }
      }

      // Mapear os itens para o formato esperado pela DataTable
      const influencersFormatted = data.itens?.map((item: any) => {
        // Encontrar os dados do influenciador
        const influencerData = influencersData.find(inf => inf.id === item.itemId);

        // Calcular total de seguidores de todas as redes
        const totalFollowers = influencerData?.totalFollowers || Math.max(
          influencerData?.socialNetworks?.instagram?.followers || 0,
          influencerData?.socialNetworks?.tiktok?.followers || 0,
          influencerData?.socialNetworks?.youtube?.followers || 0,
          influencerData?.socialNetworks?.twitter?.followers || 0,
          influencerData?.socialNetworks?.facebook?.followers || 0
        ) || 0;

        return {
          id: item.itemId,
          nome: influencerData?.name || `Influenciador ${item.itemId}`,
          usuario: `@${(influencerData?.name || 'usuario').toLowerCase().replace(/\s+/g, '')}`,
          email: influencerData?.email,
          telefone: influencerData?.phone,
          whatsapp: influencerData?.whatsapp,

          // Localização
          pais: influencerData?.country,
          estado: influencerData?.state,
          cidade: influencerData?.city,
          localizacao: influencerData?.city && influencerData?.state ?
                      `${influencerData.city}, ${influencerData.state}` :
                      influencerData?.location || 'Não informado',

          // Demografia
          idade: influencerData?.age,
          genero: influencerData?.gender,

          // Métricas
          seguidores: totalFollowers,
          engajamento: influencerData?.engagementRate || Math.floor(Math.random() * 10) + 1,
          rating: influencerData?.rating || 0,

          // Categorização
          nicho: influencerData?.category || 'Não informado',
          categorias: influencerData?.categories || [],
          categoriasprincipais: influencerData?.mainCategories || [],
          tiposConteudo: influencerData?.contentTypes || [],
          especialidades: influencerData?.specialties || [],

          // Status e verificação
          foto: influencerData?.avatar,
          status: influencerData?.status === 'active' ? 'ativo' :
                 influencerData?.status === 'inactive' ? 'inativo' :
                 influencerData?.status || 'ativo',
          verificado: influencerData?.isVerified || false,
          disponivel: influencerData?.isAvailable !== false,

          // Profissional
          promoteTraders: influencerData?.promotesTraders || false,
          nomeResponsavel: influencerData?.responsibleName,
          nomeAgencia: influencerData?.agencyName,

          // Bio e visual
          bio: influencerData?.bio,
          imagemFundo: influencerData?.backgroundImage,
          gradiente: influencerData?.gradient,

          // Audiência
          generoAudiencia: influencerData?.audienceGender,
          idiomasConteudo: influencerData?.contentLanguages || [],

          // Redes sociais detalhadas
          redesSociais: influencerData?.socialNetworks,
          instagram: influencerData?.socialNetworks?.instagram,
          tiktok: influencerData?.socialNetworks?.tiktok,
          youtube: influencerData?.socialNetworks?.youtube,
          twitter: influencerData?.socialNetworks?.twitter,
          facebook: influencerData?.socialNetworks?.facebook,
          outrasRedes: influencerData?.socialNetworks?.others || [],

          // Tags e notas
          tags: influencerData?.tags || [],
          notas: influencerData?.notes || [],

          // Campos específicos da lista
          listItemId: item.id,
          dataAdicao: item.dataAdicao,
          posicao: item.posicao,

          // Dados completos para referência
          dadosCompletos: influencerData
        };
      }) || [];

      setItems(influencersFormatted);
      console.log('📊 Influenciadores formatados:', influencersFormatted.length);

    } catch (error) {
      console.error('❌ Erro ao carregar itens da lista:', error);
      toast.error('Erro ao carregar itens da lista');
      setItems([]);
    } finally {
      setLoadingItems(false);
    }
  }, [firebaseUser, listId]);

  // Função para carregar itens por tipo - memoizada para evitar loops
  const loadItemsByType = useCallback(async (tipoObjeto: string) => {
    switch (tipoObjeto) {
      case 'influenciadores':
        await loadInfluencersFromList();
        break;
      case 'marcas':
        setItems(getMockMarcas());
        break;
      case 'campanhas':
        setItems(getMockCampanhas());
        break;
      case 'conteúdo':
        setItems(getMockConteudo());
        break;
      default:
        setItems([]);
    }
  }, [loadInfluencersFromList]);

  // Função para carregar dados da lista - memoizada para evitar loops
  const loadListaData = useCallback(async () => {
    try {
      setLoadingItems(true);

      // Mock data - em um projeto real, viria da API
      const mockLista: Lista = {
        id: listId!,
        nome: 'Top Influenciadores Fashion',
        tipoLista: 'dinâmica',
        tipoObjeto: 'influenciadores',
        tamanho: 145,
        criadoPor: currentUser!.id,
        criadoPorNome: currentUser!.name || 'Usuário',
        dataCriacao: new Date('2024-01-15T00:00:00.000Z'),
        ultimaAtualizacao: new Date('2024-06-20T00:00:00.000Z'),
        descricao: 'Lista que atualiza automaticamente com influencers de +50k seguidores em moda',
        tags: ['fashion', 'lifestyle', 'top-tier'],
        criterios: [
          { campo: 'seguidores', operador: '>', valor: '50000' },
          { campo: 'nicho', operador: '=', valor: 'fashion' },
          { campo: 'engajamento', operador: '>', valor: '3' }
        ]
      };

      setLista(mockLista);
      setDataLoaded(true); // Marcar que os dados foram carregados

      // Carregar itens baseado no tipo - chamada direta para evitar dependência circular
      if (mockLista.tipoObjeto === 'influenciadores') {
        await loadInfluencersFromList();
      } else {
        // Para outros tipos, usar dados mock
        switch (mockLista.tipoObjeto) {
          case 'marcas':
            setItems(getMockMarcas());
            break;
          case 'campanhas':
            setItems(getMockCampanhas());
            break;
          case 'conteúdo':
            setItems(getMockConteudo());
            break;
          default:
            setItems([]);
        }
      }

    } catch (error) {
      console.error('Erro ao carregar lista:', error);
      toast.error('Erro ao carregar dados da lista');
    } finally {
      setLoadingItems(false);
    }
  }, [listId, currentUser]); // Removido loadItemsByType para evitar dependência circular

  // Carregar dados da lista quando componente monta ou dependências mudam
  useEffect(() => {
    console.log('🔄 useEffect loadListaData - Verificando condições:', {
      currentUser: !!currentUser,
      userId,
      listId,
      isOwnProfile,
      loadingItems,
      dataLoaded,
      listaId: lista?.id
    });

    // Verificações de segurança para evitar loops infinitos
    if (!currentUser || !userId || !listId || !isOwnProfile) {
      console.log('❌ useEffect loadListaData - Condições básicas não atendidas');
      return;
    }

    // Evitar múltiplas execuções desnecessárias
    if (loadingItems) {
      console.log('❌ useEffect loadListaData - Já está carregando');
      return;
    }

    // Evitar execução se já temos dados da lista
    if (dataLoaded && lista && lista.id === listId) {
      console.log('❌ useEffect loadListaData - Dados já carregados');
      return;
    }

    console.log('✅ useEffect loadListaData - Executando loadListaData');
    loadListaData();
  }, [currentUser?.id, userId, listId, isOwnProfile]); // Removido loadingItems e loadListaData para evitar loops



  // Dados reais serão carregados da API

  const getMockMarcas = (): Marca[] => [
    {
      id: '1',
      nome: 'Nike Brasil',
      setor: 'Esporte',
      orcamento: 50000,
      status: 'ativo',
      contato: '<EMAIL>',
      funcionarios: 500
    },
    {
      id: '2',
      nome: 'Adidas',
      setor: 'Esporte',
      orcamento: 45000,
      status: 'negociando',
      contato: '<EMAIL>',
      funcionarios: 350
    }
  ];

  const getMockCampanhas = (): Campanha[] => [
    {
      id: '1',
      nome: 'Verão 2024',
      brand: 'Nike',
      budget: 25000,
      status: 'ativo',
      dataInicio: new Date('2024-01-01'),
      dataFim: new Date('2024-03-31'),
      alcance: 1500000
    },
    {
      id: '2',
      nome: 'Black Friday',
      brand: 'Adidas',
      budget: 40000,
      status: 'planejamento',
      dataInicio: new Date('2024-11-20'),
      dataFim: new Date('2024-11-30'),
      alcance: 2000000
    }
  ];

  const getMockConteudo = (): Conteudo[] => [
    {
      id: '1',
      nome: 'Look do Dia - Verão',
      formato: 'video',
      tamanho: 15.5,
      dataUpload: new Date('2024-06-15'),
      engajamento: 8.2,
      autor: 'Ana Silva'
    },
    {
      id: '2',
      nome: 'Tutorial Maquiagem',
      formato: 'video',
      tamanho: 22.1,
      dataUpload: new Date('2024-06-10'),
      engajamento: 6.7,
      autor: 'Julia Fashion'
    }
  ];

  // Funções de utilidade
  const getTipoObjetoIcon = (tipo: string) => {
    switch (tipo) {
      case 'influenciadores': return <Users className="h-4 w-4" />;
      case 'marcas': return <Tag className="h-4 w-4" />;
      case 'campanhas': return <Calendar className="h-4 w-4" />;
      case 'conteúdo': return <ImageIcon className="h-4 w-4" />;
      default: return <List className="h-4 w-4" />;
    }
  };



  // Função para lidar com perfis adicionados
  const handleProfilesAdded = () => {
    // Recarregar dados da lista
    if (lista) {
      loadItemsByType(lista.tipoObjeto);
    }
  };

  // Função para enviar influenciadores selecionados para proposta
  const handleSendToProposal = () => {
    if (selectedItems.length === 0) {
      toast.error('Selecione pelo menos um influenciador');
      return;
    }
    setShowSendToProposalModal(true);
  };

  // Função chamada após sucesso no envio para proposta
  const handleProposalSendSuccess = () => {
    resetSelection();
    setShowSendToProposalModal(false);
  };

  // Função para formatar influenciadores para o modal
  const getSelectedInfluencersForModal = () => {
    return selectedItems.map(itemId => {
      const item = items.find(inf => inf.id === itemId);
      return {
        id: item?.id || itemId,
        nome: item?.nome || 'Influenciador',
        foto: item?.foto,
        seguidores: item?.seguidores || 0,
        nicho: item?.nicho || 'Não informado'
      };
    });
  };

  // Usando estrutura centralizada com configuração estável
  const dataTableConfig = useMemo(() => ({
    enableColumnOrdering: true,
    enableRowSelection: true,
    pageSize: 20
  }), []);

  const { dataTableProps, selectedItems, resetSelection } = useDataTable<InfluencerType>(dataTableConfig);

  // Items para exibição (DataTable gerencia filtros internamente)
  const filteredItems = items;

  // Handlers memoizados para as colunas
  const columnHandlers = useMemo(() => ({
    onView: (influencer: any) => toast.success(`Visualizando ${influencer.nome}`),
    onEdit: (influencer: any) => toast.success(`Editando ${influencer.nome}`),
    onRemoveFromList: (influencer: any) => {
      if (confirm(`Remover ${influencer.nome} da lista?`)) {
        setItems(prev => prev.filter(item => item.id !== influencer.id));
        toast.success(`${influencer.nome} removido da lista!`);
      }
    }
  }), []);

  const columnOptions = useMemo(() => ({
    showPseudonimo: true,
    showOrigem: true,
    isListView: true
  }), []);

  // Colunas usando helper centralizado
  const influencerColumns = useMemo(() =>
    createInfluencerColumns(columnHandlers, columnOptions),
    [columnHandlers, columnOptions]
  );

  // Loading states
  if (isLoading || !userId || !listId) {
    return <Loader isLoading={true} message="" showLogo={true} />;
  }

  if (!currentUser || !canAccess) {
    return (
      <div className="p-6">
        <div className="text-center">
          <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-xl font-semibold mb-2">Acesso Negado</h2>
          <p className="text-muted-foreground">
            Você só pode acessar suas próprias listas.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex  h-screen">
      {/* Sidebar com detalhes da lista */}
      <div className="w-96 border-r bg-background dark:bg-[#080210]">
        <div className="p-6 space-y-6">
          {/* Header com botão voltar */}
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-lg font-semibold">Detalhes da Lista</h1>
              <p className="text-sm text-muted-foreground">
                Gerencie os itens da sua lista
              </p>
            </div>
          </div>

          {lista && (
            <>
              {/* Perfil da Lista */}
              <Card>
                <CardContent className="p-6">
                  <div className="text-center space-y-4">
                    {/* Avatar da lista */}
                    <div className="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-[#ec003f] to-[#9810fa] flex items-center justify-center">
                      {lista.tipoObjeto === 'influenciadores' && <Users className="h-8 w-8 text-white" />}
                      {lista.tipoObjeto === 'marcas' && <Tag className="h-8 w-8 text-white" />}
                      {lista.tipoObjeto === 'campanhas' && <Calendar className="h-8 w-8 text-white" />}
                      {lista.tipoObjeto === 'conteúdo' && <ImageIcon className="h-8 w-8 text-white" />}
                      {!['influenciadores', 'marcas', 'campanhas', 'conteúdo'].includes(lista.tipoObjeto) && <List className="h-8 w-8 text-white" />}
                    </div>

                    {/* Nome da lista */}
                    <div>
                      <h2 className="text-xl text-gray-700 font-bold dark:text-white">{lista.nome}</h2>
                    </div>

                    {/* Quantidade na lista */}
                    <div>
                      <div className="text-3xl font-bold text-[#ec003f]">{filteredItems.length}</div>
                      <div className="text-sm text-muted-foreground">
                        {lista.tipoObjeto} na lista
                      </div>
                    </div>

                    {/* Tipo da lista */}
                    <div>
                      <Badge className={lista.tipoLista === 'estática' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}>
                        Lista {lista.tipoLista}
                      </Badge>
                    </div>

                    {/* Ícones de ação */}
                    <div className="flex justify-center gap-4 pt-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-10 w-10 rounded-full hover:bg-[#ff0074] hover:text-white transition-colors"
                        onClick={() => setShowShareDialog(true)}
                        title="Compartilhar Lista"
                      >
                        <Share2 className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-10 w-10 rounded-full hover:bg-muted"
                        onClick={() => toast.success('Funcionalidade de exportar em breve!')}
                        title="Exportar Lista"
                      >
                        <Download className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-10 w-10 rounded-full hover:bg-destructive hover:text-destructive-foreground"
                        onClick={() => {
                          if (confirm(`Tem certeza que deseja deletar a lista "${lista.nome}"?`)) {
                            toast.success('Lista deletada com sucesso!');
                            router.back();
                          }
                        }}
                        title="Deletar Lista"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              
            </>
          )}
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 bg-muted/30 dark:bg-[#080210] overflow-hidden">
        <div className="h-full p-6 space-y-6 overflow-y-auto">
          {/* Navegação por Abas */}
          <div className="border-b border-border">
            <div className="flex space-x-8">
              <button
                onClick={() => setActiveTab('perfis')}
                className={cn(
                  "pb-3 px-1 border-b-2 font-medium text-sm transition-colors",
                  activeTab === 'perfis'
                    ? "border-[#ec003f] text-[#ec003f]"
                    : "border-transparent text-muted-foreground hover:text-foreground"
                )}
              >
                Perfis
                <span className="ml-2 bg-muted text-muted-foreground px-2 py-1 rounded-full text-xs">
                  {filteredItems.length}
                </span>
              </button>
              
              <button
                onClick={() => setActiveTab('agregados')}
                className={cn(
                  "pb-3 px-1 border-b-2 font-medium text-sm transition-colors",
                  activeTab === 'agregados'
                    ? "border-[#ec003f] text-[#ec003f]"
                    : "border-transparent text-muted-foreground hover:text-foreground"
                )}
              >
                Dados agregados
              </button>
            </div>
          </div>

          {/* Conteúdo das Abas */}
          {activeTab === 'perfis' && (
            <>
              {/* Botões de ação */}
              <div className="flex justify-between items-center mb-4">
                {/* Botão para enviar selecionados para proposta */}
                {selectedItems.length > 0 && (
                  <Button 
                    className="bg-[#9810fa] hover:bg-[#4a00b4] text-white"
                    onClick={handleSendToProposal}
                  >
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Enviar {selectedItems.length} para Proposta
                  </Button>
                )}

                {/* Botão para adicionar perfis */}
                {filteredItems.length > 0 && (
                  <Button 
                    className="bg-[#ff0074] hover:bg-[#d10037] text-white ml-auto"
                    onClick={() => setShowAddProfileModal(true)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Adicionar Perfis
                  </Button>
                )}
              </div>

              {/* DataTable de Perfis */}
              {loadingItems ? (
                <div className="flex items-center justify-center py-12">
                                      <Loader isLoading={true} message="" showLogo={true} />
                </div>
              ) : filteredItems.length === 0 ? (
                <Card>
                  <CardContent className="p-12 text-center">
                    {getTipoObjetoIcon(lista?.tipoObjeto || '')}
                    <h3 className="text-lg font-medium mt-4">Nenhum item encontrado</h3>
                    <p className="text-muted-foreground mb-4">
                      Esta lista ainda não possui itens.
                    </p>
                    <Button 
                      className="bg-[#ec003f] hover:bg-[#d10037]"
                      onClick={() => setShowAddProfileModal(true)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Adicionar Primeiro Item
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <DataTable
                  columns={influencerColumns}
                  data={filteredItems as InfluencerType[]}
                  searchKey="nome"
                  searchPlaceholder={`Buscar ${lista?.tipoObjeto || 'itens'}...`}
                  className="w-full"
                  enableRowSelection={dataTableConfig.enableRowSelection}
                  enableColumnOrdering={dataTableConfig.enableColumnOrdering}
                  pageSize={dataTableConfig.pageSize}
                  onRowSelectionChange={dataTableProps.onRowSelectionChange}
                  columnOrder={dataTableProps.columnOrder}
                  onColumnOrderChange={dataTableProps.onColumnOrderChange}
                />
              )}
            </>
          )}

          {/* Aba Dados Agregados */}
          {activeTab === 'agregados' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">Dados Agregados</h3>
                <p className="text-muted-foreground">
                  Esta seção será implementada em breve.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modal de Adicionar Perfis */}
      <AddProfileToListModal
        isOpen={showAddProfileModal && !!lista}
        onClose={() => setShowAddProfileModal(false)}
        listId={lista?.id || ''}
        onSuccess={handleProfilesAdded}
      />

      {/* Modal de Compartilhamento */}
      <ShareListDialog
        isOpen={showShareDialog}
        onClose={() => setShowShareDialog(false)}
        listName={lista?.nome || ''}
        listId={lista?.id || ''}
        listType={lista?.tipoObjeto || ''}
        itemCount={filteredItems.length}
      />

      {/* Modal de Envio para Proposta */}
      <SendToProposalModal
        isOpen={showSendToProposalModal}
        onClose={() => setShowSendToProposalModal(false)}
        selectedInfluencers={getSelectedInfluencersForModal()}
        onSuccess={handleProposalSendSuccess}
      />
    </div>
  );
}

"use client"

import React, { useState, useEffect } from 'react';
import { InfluencerCard } from './influencer-card';
import { Influencer } from './types';
import { useProposalBudgets } from '@/hooks/use-budgets-graphql';
import { gql, useQuery } from '@apollo/client';

// 🆕 TIPOS PARA O GRID VIEW
type InfluencerStatus = 'pendente' | 'aceito' | 'rejeitado' | 'descartado';

interface GridViewProps {
  influencers: Influencer[];
  onInfluencerClick: (influencer: Influencer) => void;
  selectedInfluencers: string[];
  selectedInfluencer?: Influencer | null;
  selectionMode: boolean;
  onToggleSelection: (id: string) => void;
  onDuplicate?: (influencer: Influencer) => void;
  onEdit?: (influencer: Influencer) => void;
  onDelete?: (influencer: Influencer) => void;
  selectedInfluencerId?: string | null;
  selectedProposal?: any;
  userId?: string; // 🆕 Necessário para hooks GraphQL
}

// 🆕 QUERY PARA BUSCAR STATUS DOS INFLUENCERS NA PROPOSTA
const GET_PROPOSAL_INFLUENCERS_STATUS = gql`
  query GetProposalInfluencersStatus($proposalId: ID!, $userId: ID!) {
    proposalInfluencersStatus(proposalId: $proposalId, userId: $userId) {
      influencerId
      status
      addedAt
      updatedAt
      addedBy
      updatedBy
    }
  }
`;

export function GridView({
  influencers,
  onInfluencerClick,
  selectedInfluencers,
  selectedInfluencer,
  selectionMode,
  onToggleSelection,
  onDuplicate,
  onEdit,
  onDelete,
  selectedInfluencerId,
  selectedProposal,
  userId
}: GridViewProps) {
  const [influencersStatus, setInfluencersStatus] = useState<Record<string, InfluencerStatus>>({});

  // 🆕 BUSCAR STATUS DOS INFLUENCERS NA PROPOSTA via GraphQL
  const { data: proposalStatusData, loading: statusLoading, error: statusError, refetch } = useQuery(
    GET_PROPOSAL_INFLUENCERS_STATUS,
    {
      variables: {
        proposalId: selectedProposal?.id || '',
        userId: userId || ''
      },
      skip: !selectedProposal?.id || !userId,
      fetchPolicy: 'cache-and-network', // Cache first, mas sempre buscar dados atualizados
      errorPolicy: 'all',
      pollInterval: 15000, // Atualizar a cada 15 segundos
      notifyOnNetworkStatusChange: true
    }
  );

  // 🆕 useEffect para mapear status dos influencers baseado na resposta GraphQL
  useEffect(() => {
    if (!selectedProposal || !proposalStatusData?.proposalInfluencersStatus || influencers.length === 0) {
      setInfluencersStatus({});
      return;
    }

    // Criar mapa de status baseado nos dados da subcoleção
    const statusMap: Record<string, InfluencerStatus> = {};
    
    influencers.forEach((influencer: Influencer) => {
      const influencerId = String(influencer.id);
      
      // Encontrar o status deste influencer na proposta
      const influencerStatusData = proposalStatusData.proposalInfluencersStatus.find(
        (item: any) => item.influencerId === influencerId
      );

      if (influencerStatusData) {
        // Mapear o status do Firebase para o tipo correto
        const status = influencerStatusData.status || 'pendente';
        statusMap[influencerId] = status as InfluencerStatus;
      } else {
        // Se não encontrou o influencer na proposta, provavelmente não foi adicionado ainda
        statusMap[influencerId] = 'pendente';
      }
    });

    console.log('🔍 [GridView] Status dos influencers na proposta atualizado:', {
      selectedProposal: selectedProposal?.id,
      influencersCount: influencers.length,
      statusDataCount: proposalStatusData?.proposalInfluencersStatus?.length || 0,
      statusMap
    });

    setInfluencersStatus(statusMap);
  }, [selectedProposal, proposalStatusData, influencers]);

  // 🆕 Hook para refetch quando a proposta mudar
  useEffect(() => {
    if (selectedProposal?.id && userId) {
      console.log('🔄 [GridView] Proposta mudou, atualizando status dos influencers...');
      refetch();
    }
  }, [selectedProposal?.id, userId, refetch]);

  // 🆕 Função para forçar atualização do status (pode ser chamada após mudanças)
  const refreshStatus = () => {
    console.log('🔄 [GridView] Forçando atualização do status dos influencers...');
    refetch();
  };

  return (
    <div className="grid grid-cols-[repeat(auto-fit,minmax(17rem,1fr))] gap-3">
      {influencers.map((influencer: Influencer) => (
        <InfluencerCard
          key={influencer.id}
          influencer={influencer}
          onClick={onInfluencerClick}
          isSelected={selectionMode ? selectedInfluencers.includes(String(influencer.id)) : selectedInfluencer?.id === influencer.id}
          selectionMode={selectionMode}
          onToggleSelection={onToggleSelection}
          onDuplicate={onDuplicate}
          onEdit={onEdit}
          onDelete={onDelete}
          isSelectedFromUrl={selectedInfluencerId === String(influencer.id)}
          isInProposal={!!selectedProposal}
          proposalId={selectedProposal?.id}
          influencerStatus={influencersStatus[String(influencer.id)] || null}
        />
      ))}
    </div>
  );
}



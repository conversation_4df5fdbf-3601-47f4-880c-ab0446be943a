'use client';

import { useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@clerk/nextjs';
import { Loader2 } from 'lucide-react';

/**
 * Componente interno que usa useSearchParams
 */
function SharedInfluencersRedirectContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { userId, isLoaded } = useAuth();

  useEffect(() => {
    if (!isLoaded) return;

    // Se não há parâmetros de compartilhamento, redirecionar para login
    const shared = searchParams?.get('shared');
    const ids = searchParams?.get('ids');
    const token = searchParams?.get('token');

    if (shared !== 'true' || !ids || !token) {
      console.log('🔗 [SHARED_REDIRECT] Parâmetros de compartilhamento inválidos, redirecionando para home');
      router.replace('/');
      return;
    }

    // Se usuário não está logado, redirecionar para login mantendo os parâmetros
    if (!userId) {
      console.log('🔗 [SHARED_REDIRECT] Usuário não logado, redirecionando para login');
      const currentUrl = window.location.href;
      const loginUrl = `/sign-in?redirect_url=${encodeURIComponent(currentUrl)}`;
      router.replace(loginUrl);
      return;
    }

    // Redirecionar para a página correta do usuário mantendo os parâmetros
    const targetUrl = `/${userId}/influencers?${searchParams?.toString()}`;
    console.log('🔗 [SHARED_REDIRECT] Redirecionando para:', targetUrl);
    router.replace(targetUrl);

  }, [isLoaded, userId, searchParams, router]);

  // Mostrar loading enquanto processa o redirecionamento
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-[#ff0074]" />
        </div>
        <div className="space-y-2">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Carregando Lista Compartilhada
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Redirecionando você para visualizar a lista...
          </p>
        </div>
      </div>
    </div>
  );
}

/**
 * Componente de loading para Suspense
 */
function LoadingFallback() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-[#ff0074]" />
        </div>
        <div className="space-y-2">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Carregando...
          </h2>
        </div>
      </div>
    </div>
  );
}

/**
 * Página de redirecionamento para listas compartilhadas
 * Redireciona para /[userId]/influencers mantendo os parâmetros de compartilhamento
 */
export default function SharedInfluencersRedirect() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <SharedInfluencersRedirectContent />
    </Suspense>
  );
}

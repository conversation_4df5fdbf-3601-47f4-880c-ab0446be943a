import React from 'react';
import { Badge } from '@/components/ui/badge';
import { AiIcon } from '@/components/ui/ai-icon';
import { useTranslations } from '@/hooks/use-translations';

export interface AudienceInterest {
  category: string;
  percentage: number;
}

interface AudienceInterestsProps {
  interests: AudienceInterest[];
  title?: string;
  className?: string;
  maxItems?: number;
}

export function AudienceInterests({ 
  interests, 
  title,
  className = "",
  maxItems = 8 
}: AudienceInterestsProps) {
  const { t } = useTranslations();
  const finalTitle = title || t('influencers.audience_interests');
  
  // Ordenar por percentual (maior para menor) e limitar itens
  const sortedInterests = interests
    .filter(item => item.percentage > 0)
    .sort((a, b) => b.percentage - a.percentage)
    .slice(0, maxItems);

  if (sortedInterests.length === 0) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-semibold text-sm text-gray-900 dark:text-white">
            {finalTitle}
          </h4>
          <AiIcon 
            size={26} 
            className="text-gray-600 dark:text-gray-400 opacity-80 hover:opacity-100 transition-opacity"
          />
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {t('influencers.no_interests_available')}
        </p>
      </div>
    );
  }

  // Usar apenas cores preto e branco para os badges de interesses
  const getInterestColor = (index: number) => {
    // Alternando entre tons de cinza para criar contraste visual
    const colors = [
      'bg-white text-black dark:bg-black dark:text-white',
      
    ];
    return colors[index % colors.length];
  };

  return (
    <div className={`p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h4 className="font-semibold text-sm text-gray-900 dark:text-white">
          {finalTitle}
        </h4>
        <AiIcon 
          size={18} 
          className="text-gray-600 dark:text-gray-400 opacity-80 hover:opacity-100 transition-opacity"
        />
      </div>
      
      <div className="flex flex-wrap gap-2">
        {sortedInterests.map((interest, index) => (
          <Badge 
            key={`${interest.category}-${index}`}
            variant="secondary"
            className={`
              ${getInterestColor(index)}
              px-3 py-1 text-xs font-medium
              border border-current/20
              transition-all duration-200
              hover:scale-105 hover:shadow-sm
            `}
          >
            <span className="font-medium">{interest.category}</span>
            <span className="ml-1 opacity-75">{interest.percentage.toFixed(1)}%</span>
          </Badge>
        ))}
      </div>
      
      {interests.length > maxItems && (
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-3">
          Mostrando {maxItems} de {interests.length} interesses
        </p>
      )}
    </div>
  );
} 

